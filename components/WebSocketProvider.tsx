"use client";

import { session<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/constants/session";
import { CareerAgentAPI, Message, MessageType } from "@/lib/api";
import { useAppDispatch } from "@/lib/store/hooks";
import React, { useCallback, useEffect, useRef } from "react";
import { IItem } from "@/lib/common";
import { ChatMessageRole, SocketStatus } from "@/constants/enum";
import {
  saveSessionID,
  setConnected,
  setDisconnected,
  setStatus,
} from "@/lib/store/features/websocket/websocketSlice";
import { setSocket } from "@/lib/socket";
import { updateItem } from "@/lib/store/features/websocket/websocketSlice"; // <-- Add this import

export default function WebSocketProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const wsRef = useRef<CareerAgentAPI | null>(null);

  const dispatch = useAppDispatch();

  const updateItems = useCallback(
    (e: Record<string, unknown>) => {
      const message: Message = {
        session_id: e.session_id as string,
        message_id: e.message_id as string,
        message_type: e.message_type as string,
        position_id: e.position_id as string,
        message: e.message as string,
      };

      const newItem: IItem = {
        role: ChatMessageRole.Agent,
        itemId: message.message_id,
        message: "",
      };

      if (message.message_type == MessageType.POSITION_SEARCH_RESULT) {
        const positionSearchResult = JSON.parse(message.message);
        newItem["positionSearchResult"] = positionSearchResult;
      } else {
        newItem["message"] = message.message;
      }

      dispatch(
        updateItem({
          newItem: newItem,
          positionID: message.position_id,
        })
      );
    },
    [dispatch]
  );

  const onResponseStart = useCallback((e: Record<string, unknown>) => {
    console.debug("onResponseStart", e);

    dispatch(saveSessionID(e.session_id as string));
    dispatch(setStatus(SocketStatus.MessageReceiving));
    console.debug(`websocket status: ${SocketStatus.MessageReceiving}`);

    updateItems(e);
  }, [dispatch, updateItems]);

  const onResponseDelta = useCallback((e: Record<string, unknown>) => {
    console.debug("onResponseDelta", e);

    updateItems(e);
  }, [updateItems]);

  const onResponseEnd = useCallback((e: Record<string, unknown>) => {
    console.debug("onResponseEnd", e);

    dispatch(setStatus(SocketStatus.MessageReceived));
    console.debug(`websocket status: ${SocketStatus.MessageReceived}`);
  }, [dispatch]);

  const onResponseError = useCallback((e: Record<string, unknown>) => {
    // TODO:  エラー処理
    console.error("onResponseError", e);

    dispatch(setStatus(SocketStatus.ErrorReceived));
    console.debug(`websocket status: ${SocketStatus.ErrorReceived}`);
  }, [dispatch]);

  const connect = useCallback(() => {
    if (wsRef.current && wsRef.current.isConnected()) {
      console.log("[WebSocket] already connected");
      return;
    }

    const ws = new CareerAgentAPI({
      url: process.env.NEXT_PUBLIC_AGENT_ENDPOINT || "",
      debug: process.env.NODE_ENV !== "production",
    });
    wsRef.current = ws;

    ws.on("close", () => {
      dispatch(setDisconnected());
      console.debug(`websocket status: ${SocketStatus.Disconnected}`);
      console.warn("[WebSocket] closed, reconnecting in 10s");
      setTimeout(connect, 10000);
    });

    const sessionID = localStorage.getItem(sessionStorageKey);
    ws.connect(sessionID).then(() => {
      ws.on("server.response.start", onResponseStart);
      ws.on("server.response.delta", onResponseDelta);
      ws.on("server.response.end", onResponseEnd);
      ws.on("server.response.error", onResponseError);

      setSocket(ws);
      dispatch(setConnected(sessionID));
      console.debug(`websocket status: ${SocketStatus.Connected}`);
    });
  }, [wsRef, dispatch, onResponseStart, onResponseDelta, onResponseEnd, onResponseError]);

  const disconnect = useCallback(() => {
    wsRef.current?.disconnect();
    dispatch(setDisconnected());
    console.debug(`websocket status: ${SocketStatus.Disconnected}`);

    wsRef.current?.off("server.response.start", onResponseStart);
    wsRef.current?.off("server.response.delta", onResponseDelta);
    wsRef.current?.off("server.response.end", onResponseEnd);
    wsRef.current?.off("server.response.error", onResponseError);

    wsRef.current = null;
  }, [wsRef, dispatch, onResponseStart, onResponseDelta, onResponseEnd, onResponseError]);

  useEffect(() => {
    connect();

    const handleUnload = () => {
      disconnect();
    };
    window.addEventListener("beforeunload", handleUnload);
    return () => {
      disconnect();
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [connect, disconnect]);

  return children;
}
